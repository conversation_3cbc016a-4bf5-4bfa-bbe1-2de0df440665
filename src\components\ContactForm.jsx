import React, { useState, useEffect } from 'react';
import emailjs from '@emailjs/browser';
import { toast } from 'react-toastify';

export default function ContactForm() {
  // Initialize EmailJS
  useEffect(() => {
    emailjs.init('xT5_SFGhfJZhSmwZ2'); // Your public key
  }, []);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  // Handler for input field changes
  const handleInputChange = event => {
    const { name, value } = event.target;
    setFormData(prevFormData => ({
      ...prevFormData,
      [name]: value,
    }));
  };

  const onSubmit = async event => {
    event.preventDefault();
    setLoading(true);

    console.log('🚀 EmailJS Form Submission Started');
    console.log('Form Data:', formData);

    try {
      // EmailJS configuration
      const serviceID = 'service_l3kiohx';
      const templateID = 'template_7dfyeuo'; // Your actual template ID from EmailJS

      // Template parameters for EmailJS - matching your template variables
      const templateParams = {
        name: formData.name,           // {{name}} in your template
        message: formData.message,     // {{message}} in your template
        time: new Date().toLocaleString('en-IN', {
          timeZone: 'Asia/Kolkata',
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }), // {{time}} in your template
        email: formData.email,         // {{email}} in your template
        subject: formData.subject,     // {{subject}} in your template
      };

      console.log('Sending email with params:', templateParams);

      const result = await emailjs.send(serviceID, templateID, templateParams);

      console.log('EmailJS Result:', result);

      if (result.status === 200) {
        // Clear form data
        setFormData({ name: '', email: '', subject: '', message: '' });

        // Show success toast
        toast.success('🎉 Message sent successfully! I\'ll get back to you soon.', {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });

        console.log('Email sent successfully!');
      } else {
        throw new Error('Email sending failed');
      }
    } catch (error) {
      console.error('EmailJS Error:', error);

      // Show error toast
      toast.error('❌ Failed to send message. Please try again or contact me directly.', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      // More detailed error logging
      if (error.text) {
        console.error('Error details:', error.text);
      }
    } finally {
      setLoading(false);
    }
  };
  return (
    <form id="contact-form" onSubmit={onSubmit}>
      <div className="row gx-3 gy-4">
        <div className="col-md-6">
          <div className="form-group">
            <label className="form-label">First Name</label>
            <input
              name="name"
              placeholder="Name *"
              className="form-control"
              type="text"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </div>
        </div>
        <div className="col-md-6">
  <div className="form-group">
    <label className="form-label">Last Name</label>
    <input
      name="lastName"
      placeholder="Last Name"
      className="form-control"
      type="text"
      value={formData.lastName}
      onChange={handleInputChange}     
    />
  </div>
</div>
        <div className="col-md-6">
  <div className="form-group">
    <label className="form-label">Phone Number</label>
    <input
      name="phone"
      placeholder="Phone Number *"
      className="form-control"
      type="tel"
      value={formData.phone}
      onChange={handleInputChange}
      required
      pattern="[0-9]{10}" // optional validation for 10-digit numbers
    />
  </div>
</div>
        <div className="col-md-6">
          <div className="form-group">
            <label className="form-label">Email</label>
            <input
              name="email"
              placeholder="Email"
              className="form-control"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
            
            />
          </div>
        </div>
        <div className="col-12">
          <div className="form-group">
            <label className="form-label">Subject</label>
            <input
              name="subject"
              placeholder="Subject *"
              className="form-control"
              type="text"
              value={formData.subject}
              onChange={handleInputChange}
              required
            />
          </div>
        </div>
        <div className="col-md-12">
          <div className="form-group">
            <label className="form-label">message</label>
            <textarea
              name="message"
              placeholder="Your message"
              rows={4}
              className="form-control"
              value={formData.message}
              onChange={handleInputChange}
            />
          </div>
        </div>
        <div className="col-md-12">
          <div className="send">
            <button
              className={`px-btn w-100 ${loading ? 'disabled' : ''}`}
              type="submit"
              disabled={loading}
            >
              {loading ? 'Sending...' : 'Send Message'}
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
