[{"C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\index.js": "1", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\App.js": "2", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\pages\\Home.jsx": "3", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Layout.jsx": "4", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Projects.jsx": "5", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\CustomCursor.jsx": "6", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Footer.jsx": "7", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Hero.jsx": "8", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Brands.jsx": "9", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Service.jsx": "10", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Testimonial.jsx": "11", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\About.jsx": "12", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Contact.jsx": "13", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Experience.jsx": "14", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Header.jsx": "15", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SectionHeading.jsx": "16", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Modal.jsx": "17", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Ratings.jsx": "18", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactInfo.jsx": "19", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SocialBtns.jsx": "20", "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactForm.jsx": "21"}, {"size": 442, "mtime": 1746806176002, "results": "22", "hashOfConfig": "23"}, {"size": 1155, "mtime": 1752327888045, "results": "24", "hashOfConfig": "23"}, {"size": 995, "mtime": 1746806177205, "results": "25", "hashOfConfig": "23"}, {"size": 419, "mtime": 1746806176506, "results": "26", "hashOfConfig": "23"}, {"size": 4377, "mtime": 1752248071613, "results": "27", "hashOfConfig": "23"}, {"size": 2237, "mtime": 1746806176705, "results": "28", "hashOfConfig": "23"}, {"size": 276, "mtime": 1746867224260, "results": "29", "hashOfConfig": "23"}, {"size": 2590, "mtime": 1746806176820, "results": "30", "hashOfConfig": "23"}, {"size": 2087, "mtime": 1750572997994, "results": "31", "hashOfConfig": "23"}, {"size": 1232, "mtime": 1752327899639, "results": "32", "hashOfConfig": "23"}, {"size": 1549, "mtime": 1752327925815, "results": "33", "hashOfConfig": "23"}, {"size": 2733, "mtime": 1746806176626, "results": "34", "hashOfConfig": "23"}, {"size": 1566, "mtime": 1746806177007, "results": "35", "hashOfConfig": "23"}, {"size": 1388, "mtime": 1752327912742, "results": "36", "hashOfConfig": "23"}, {"size": 3330, "mtime": 1746806176473, "results": "37", "hashOfConfig": "23"}, {"size": 463, "mtime": 1746806176927, "results": "38", "hashOfConfig": "23"}, {"size": 1953, "mtime": 1746806176661, "results": "39", "hashOfConfig": "23"}, {"size": 914, "mtime": 1746806176893, "results": "40", "hashOfConfig": "23"}, {"size": 589, "mtime": 1746806177085, "results": "41", "hashOfConfig": "23"}, {"size": 595, "mtime": 1746806177122, "results": "42", "hashOfConfig": "23"}, {"size": 5876, "mtime": 1752248056870, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vebk8z", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\index.js", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\App.js", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\pages\\Home.jsx", ["107"], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Layout.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Projects.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\CustomCursor.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Footer.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Hero.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Brands.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Service.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Testimonial.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\About.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Contact.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Experience.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Header.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SectionHeading.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Modal.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\Ratings.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactInfo.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\SocialBtns.jsx", [], [], "C:\\save\\Desktop\\jenna-personal-portfolio-template-2023-11-27-05-07-17-utc\\jenna-react\\src\\components\\ContactForm.jsx", [], [], {"ruleId": "108", "severity": 1, "message": "109", "line": 1, "column": 17, "nodeType": "110", "messageId": "111", "endLine": 1, "endColumn": 26}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar"]